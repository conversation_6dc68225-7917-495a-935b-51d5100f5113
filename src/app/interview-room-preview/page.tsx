"use client";

import { useState } from "react";

// Mock participant data for preview
const mockParticipants = [
  {
    identity: "john-doe-1234567890",
    isSpeaking: false,
  },
  {
    identity: "agent-interviewer",
    isSpeaking: true,
  },
];

function PreviewRoomContent() {
  const [participants] = useState(mockParticipants);
  const [showWithParticipants, setShowWithParticipants] = useState(true);

  const displayParticipants = showWithParticipants ? participants : [];

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Preview Controls */}
      <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
          🎨 UI Preview Mode
        </h3>
        <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-3">
          This is a preview of the InterviewRoom UI without LiveKit connection.
        </p>
        <div className="flex gap-2">
          <button
            onClick={() => setShowWithParticipants(true)}
            className={`px-3 py-1 text-sm rounded ${
              showWithParticipants
                ? "bg-yellow-600 text-white"
                : "bg-yellow-200 text-yellow-800 hover:bg-yellow-300"
            }`}
          >
            With Participants
          </button>
          <button
            onClick={() => setShowWithParticipants(false)}
            className={`px-3 py-1 text-sm rounded ${
              !showWithParticipants
                ? "bg-yellow-600 text-white"
                : "bg-yellow-200 text-yellow-800 hover:bg-yellow-300"
            }`}
          >
            Waiting State
          </button>
        </div>
      </div>

      {/* Actual InterviewRoom UI */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">
            Interview in Progress
          </h2>
          <p className="text-foreground/70">
            {displayParticipants.length > 1
              ? `Connected with AI Interviewer (${displayParticipants.length} participants)`
              : "Waiting for AI Interviewer to join..."}
          </p>
        </div>

        <div className="space-y-4">
          {displayParticipants.map((participant) => (
            <div
              key={participant.identity}
              className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-3 h-3 rounded-full ${
                    participant.identity.includes("agent")
                      ? "bg-blue-500"
                      : "bg-green-500"
                  }`}
                ></div>
                <span className="font-medium text-foreground">
                  {participant.identity.includes("agent")
                    ? "AI Interviewer"
                    : participant.identity}
                </span>
                <span className="text-sm text-foreground/60">
                  {participant.isSpeaking ? "🎤 Speaking" : "🔇 Silent"}
                </span>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-foreground/60 mb-4">
            Make sure your microphone is enabled and speak clearly.
          </p>
        </div>
      </div>

      {/* Navigation */}
      <div className="mt-6 text-center space-x-4">
        <a
          href="/call"
          className="inline-block px-6 py-3 bg-green-600 hover:bg-green-700
                     text-white font-medium rounded-lg transition-colors"
        >
          Start Real Interview
        </a>
        <a
          href="/"
          className="inline-block px-6 py-3 bg-gray-600 hover:bg-gray-700
                     text-white font-medium rounded-lg transition-colors"
        >
          Back to Home
        </a>
      </div>
    </div>
  );
}

export default function InterviewRoomPreview() {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <PreviewRoomContent />
    </div>
  );
}

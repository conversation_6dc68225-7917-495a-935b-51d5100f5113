"use client";

import <PERSON> from "next/link";
import Interview<PERSON><PERSON> from "@/components/InterviewRoom";

export default function InterviewRoomPreview() {
  return (
    <div>
      {/* Preview Banner */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
        <div className="max-w-4xl mx-auto p-4">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-1">
            🎨 UI Preview Mode
          </h3>
          <p className="text-yellow-700 dark:text-yellow-300 text-sm">
            This is a preview of the InterviewRoom UI without LiveKit
            connection. Any changes to the InterviewRoom component will be
            reflected here.
          </p>
          <div className="mt-3 space-x-4">
            <Link
              href="/call"
              className="inline-block px-4 py-2 bg-green-600 hover:bg-green-700
                         text-white text-sm font-medium rounded transition-colors"
            >
              Start Real Interview
            </Link>
            <Link
              href="/"
              className="inline-block px-4 py-2 bg-gray-600 hover:bg-gray-700
                         text-white text-sm font-medium rounded transition-colors"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>

      {/* Actual InterviewRoom component in preview mode */}
      <InterviewRoom previewMode={true} />
    </div>
  );
}

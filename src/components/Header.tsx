import Link from "next/link";

export default function Header() {
  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Link
            href="/"
            className="text-xl font-bold text-foreground hover:text-blue-600 transition-colors"
          >
            Interview Next Service
          </Link>

          <nav className="flex items-center space-x-6">
            <Link
              href="/questionnaire-prompt-builder"
              className="text-foreground/70 hover:text-foreground transition-colors"
            >
              Prompt Builder
            </Link>
            <Link
              href="/interview-room-preview"
              className="text-foreground/70 hover:text-foreground transition-colors"
            >
              UI Preview
            </Link>
            <Link
              href="/call"
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              Start Call
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}

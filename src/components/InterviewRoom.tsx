"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Room<PERSON>udio<PERSON><PERSON><PERSON>,
  useParticipants,
  Disconnect<PERSON><PERSON>on,
  useDataChannel,
} from "@livekit/components-react";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface InterviewRoomProps {
  roomData: RoomData;
  onDisconnect: () => void;
}

function RoomContent({ onDisconnect }: { onDisconnect?: () => void }) {
  const participants = useParticipants();
  const [agentEndingInterview, setAgentEndingInterview] = useState(false);

  // Listen for data channel messages from the agent
  // Agent should send: {"action": "end_interview", "reason": "Interview completed successfully"}
  useDataChannel("interview-control", (message) => {
    try {
      // Convert Uint8Array to string
      const textDecoder = new TextDecoder();
      const messageText = textDecoder.decode(message.payload);
      const data = JSON.parse(messageText);

      if (data.action === "end_interview") {
        console.log(
          "Agent requested to end interview:",
          data.reason || "Interview completed"
        );

        // Show visual feedback
        setAgentEndingInterview(true);

        // Give a brief moment for any final audio, then disconnect
        setTimeout(() => {
          onDisconnect?.();
        }, 3000); // 3 second delay for user to see the message
      }
    } catch (error) {
      console.error("Error parsing agent message:", error);
    }
  });

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">
            {agentEndingInterview
              ? "Interview Ending..."
              : "Interview in Progress"}
          </h2>
          <p className="text-foreground/70">
            {agentEndingInterview
              ? "The AI interviewer is concluding the session. Thank you for your time!"
              : participants.length > 1
              ? `Connected with AI Interviewer (${participants.length} participants)`
              : "Waiting for AI Interviewer to join..."}
          </p>
        </div>

        <div className="space-y-4">
          {participants.map((participant) => (
            <div
              key={participant.identity}
              className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-3 h-3 rounded-full ${
                    participant.identity.includes("agent")
                      ? "bg-blue-500"
                      : "bg-green-500"
                  }`}
                ></div>
                <span className="font-medium text-foreground">
                  {participant.identity.includes("agent")
                    ? "AI Interviewer"
                    : participant.identity}
                </span>
                <span className="text-sm text-foreground/60">
                  {participant.isSpeaking ? "🎤 Speaking" : "🔇 Silent"}
                </span>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-foreground/60 mb-4">
            Make sure your microphone is enabled and speak clearly.
          </p>

          <div className="mt-4">
            <DisconnectButton className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
              End Interview
            </DisconnectButton>
          </div>
        </div>
      </div>

      {/* This handles all audio rendering automatically */}
      <RoomAudioRenderer />
    </div>
  );
}

export default function InterviewRoom({
  roomData,
  onDisconnect,
}: InterviewRoomProps) {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <LiveKitRoom
        token={roomData.token}
        serverUrl={roomData.serverUrl}
        connect={true}
        audio={true}
        video={false}
        onDisconnected={onDisconnect}
        onError={(error) => {
          console.error("LiveKit error:", error);
        }}
        style={{ height: "100%" }}
      >
        <RoomContent onDisconnect={onDisconnect} />
      </LiveKitRoom>
    </div>
  );
}

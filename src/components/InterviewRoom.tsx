"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoomA<PERSON>o<PERSON><PERSON><PERSON>,
  useParticipants,
  ConnectionStateToast,
} from "@livekit/components-react";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface InterviewRoomProps {
  roomData?: RoomData;
  onDisconnect?: () => void;
  previewMode?: boolean;
}

// Mock participants for preview mode
const mockParticipants = [
  {
    identity: "john-doe-1234567890",
    isSpeaking: false,
  },
  {
    identity: "agent-interviewer",
    isSpeaking: true,
  },
];

function RoomContent({
  previewMode = false,
  onDisconnect,
}: {
  previewMode?: boolean;
  onDisconnect?: () => void;
}) {
  const liveParticipants = useParticipants();
  const participants = previewMode ? mockParticipants : liveParticipants;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">
            Interview in Progress
          </h2>
          <p className="text-foreground/70">
            {participants.length > 1
              ? `Connected with AI Interviewer (${participants.length} participants)`
              : "Waiting for AI Interviewer to join..."}
          </p>
        </div>

        <div className="space-y-4">
          {participants.map((participant) => (
            <div
              key={participant.identity}
              className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-3 mb-3">
                <div
                  className={`w-3 h-3 rounded-full ${
                    participant.identity.includes("agent")
                      ? "bg-blue-500"
                      : "bg-green-500"
                  }`}
                ></div>
                <span className="font-medium text-foreground flex-1">
                  {participant.identity.includes("agent")
                    ? "AI Interviewer"
                    : participant.identity}
                </span>
                <span className="text-sm text-foreground/60">
                  {participant.isSpeaking ? "🎤 Speaking" : "🔇 Silent"}
                </span>
                {!previewMode && (
                  <div
                    className="w-4 h-4 rounded-full bg-green-400 border-2 border-green-600"
                    title="Good connection"
                  />
                )}
                {previewMode && (
                  <div
                    className="w-4 h-4 rounded-full bg-green-400 border-2 border-green-600"
                    title="Connection quality (preview)"
                  />
                )}
              </div>

              {/* Audio visualizer for speaking participants */}
              {participant.isSpeaking && (
                <div className="mt-2 bg-gray-100 dark:bg-gray-600 rounded p-2">
                  <div className="text-xs text-foreground/60 mb-1">
                    Audio Level
                  </div>
                  <div className="h-6 bg-green-500 rounded animate-pulse opacity-70"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-foreground/60 mb-4">
            Make sure your microphone is enabled and speak clearly.
          </p>

          {/* Disconnect button for non-preview mode */}
          {!previewMode && (
            <div className="mt-4">
              <button
                onClick={onDisconnect}
                className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                End Interview
              </button>
            </div>
          )}
        </div>
      </div>

      {/* This handles all audio rendering automatically */}
      <RoomAudioRenderer />
    </div>
  );
}

export default function InterviewRoom({
  roomData,
  onDisconnect,
  previewMode = false,
}: InterviewRoomProps) {
  // In preview mode, just show the UI without LiveKit
  if (previewMode) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background py-6">
        <RoomContent previewMode={true} onDisconnect={onDisconnect} />
      </div>
    );
  }

  // Normal mode requires roomData
  if (!roomData) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background py-6 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold text-foreground mb-2">
            No Room Data
          </h2>
          <p className="text-foreground/70">
            Unable to connect to interview room.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <LiveKitRoom
        token={roomData.token}
        serverUrl={roomData.serverUrl}
        connect={true}
        audio={true}
        video={false}
        onDisconnected={onDisconnect}
        onError={(error) => {
          console.error("LiveKit error:", error);
        }}
        style={{ height: "100%" }}
      >
        <RoomContent previewMode={false} onDisconnect={onDisconnect} />
        <ConnectionStateToast />
      </LiveKitRoom>
    </div>
  );
}

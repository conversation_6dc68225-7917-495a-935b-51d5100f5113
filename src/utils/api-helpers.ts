// Types for API responses
export type ApiResponse<T = Record<string, unknown>> = {
  success: boolean;
  data?: T;
  error?: string;
};

/**
 * Utility function to handle API responses and extract error messages
 * Provides consistent error handling across all API calls
 */
export async function handleApiResponse<T = Record<string, unknown>>(
  response: Response
): Promise<ApiResponse<T>> {
  if (response.ok) {
    const data = await response.json();
    return { success: true, data };
  } else {
    const errorData = await response.json().catch(() => ({}));
    const error = errorData.error || `Request failed (${response.status})`;
    return { success: false, error };
  }
}

/**
 * Utility function to handle network errors
 * Provides consistent network error messaging
 */
export function handleNetworkError(error: unknown): string {
  console.error("Network error:", error);
  return "Network error: Unable to connect to server";
}

/**
 * Generic API call wrapper that handles both HTTP and network errors
 * Reduces boilerplate in components
 */
export async function makeApiCall<T = Record<string, unknown>>(
  url: string,
  options?: RequestInit
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, options);
    return await handleApiResponse<T>(response);
  } catch (error) {
    return {
      success: false,
      error: handleNetworkError(error),
    };
  }
}

/**
 * Hook-like pattern for managing API call state
 * Can be used to standardize loading/error states
 */
export type ApiCallState = {
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
};

/**
 * Validation utilities
 */
export const validation = {
  /**
   * Validates participant name for join form
   */
  participantName: (name: string): { isValid: boolean; error?: string } => {
    const trimmed = name.trim();
    if (!trimmed) {
      return { isValid: false, error: "Please enter your name" };
    }
    if (trimmed.length < 2) {
      return { isValid: false, error: "Name must be at least 2 characters long" };
    }
    if (trimmed.length > 50) {
      return { isValid: false, error: "Name must not exceed 50 characters" };
    }
    return { isValid: true };
  },

  /**
   * Validates prompt for questionnaire builder
   */
  prompt: (prompt: string): {
    isValid: boolean;
    canSave: boolean;
    tooltip: string;
    characterCountClass: string;
    error?: string;
  } => {
    const MIN_LENGTH = 10;
    const MAX_LENGTH = 10000;
    const length = prompt.length;
    const trimmedLength = prompt.trim().length;
    
    const isValid = trimmedLength >= MIN_LENGTH && length <= MAX_LENGTH;
    const canSave = isValid && prompt.trim().length > 0;
    
    let tooltip = "Save prompt (Ctrl/Cmd + S)";
    let characterCountClass = "text-foreground/60";
    let error: string | undefined;
    
    if (length > MAX_LENGTH) {
      tooltip = `Prompt exceeds maximum length (${MAX_LENGTH} characters)`;
      characterCountClass = "text-red-600 font-medium";
      error = `Prompt must not exceed ${MAX_LENGTH} characters`;
    } else if (trimmedLength < MIN_LENGTH && trimmedLength > 0) {
      tooltip = `Prompt must be at least ${MIN_LENGTH} characters long`;
      characterCountClass = "text-foreground/60";
      error = `Prompt must be at least ${MIN_LENGTH} characters long`;
    } else if (length > MAX_LENGTH * 0.9) {
      characterCountClass = "text-yellow-600 font-medium";
    }
    
    return { isValid, canSave, tooltip, characterCountClass, error };
  },
};
